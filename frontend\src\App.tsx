import { NavigationContainer, useNavigationContainerRef } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { useColorScheme, Platform } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { ErrorBoundary } from '@components';
import { AuthProvider, ThemeProvider, useThemePreferences, getEffectiveTheme } from '@contexts';
import AppNavigator from '@navigation/AppNavigator';
import { createTheme } from '@theme/variants';
import NavigationStateDebugger from './components/NavigationStateDebugger';
import '@i18n/index';

// Test if basic React Native components work
console.log('App.tsx loaded successfully');

// Import the new utilities and hooks
import { useTabVisibility } from './hooks/usePageVisibility';
import {
  loadNavigationState,
  saveNavigationState,
  shouldRestoreNavigationState,
  isValidNavigationState,
  webNavigationUtils,
  debugNavigationState
} from './utils/webNavigation';

// Inner app component that uses theme context
const AppContent: React.FC = () => {
  const { preferences, isLoading } = useThemePreferences();
  const systemColorScheme = useColorScheme();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const [initialNavigationState, setInitialNavigationState] = useState<any>(null);

  const navigationRef = useNavigationContainerRef();
  const { isTabActive } = useTabVisibility();
  const currentStateRef = useRef<any>(null);
  const isRestoringRef = useRef(false);

  const isDark = getEffectiveTheme(preferences, systemColorScheme) === 'dark';

  // Memoize theme creation to prevent unnecessary re-creations
  const theme = useMemo(() => {
    return createTheme(preferences, isDark);
  }, [preferences, isDark]);

  // Function to get current navigation state
  const getCurrentNavigationState = useCallback(() => {
    if (navigationRef.isReady()) {
      return navigationRef.getRootState();
    }
    return currentStateRef.current;
  }, [navigationRef]);

  // Load navigation state on app start
  useEffect(() => {
    const restoreState = async () => {
      try {
        if (!shouldRestoreNavigationState()) {
          console.log('📱 Skipping navigation state restoration');
          setIsNavigationReady(true);
          return;
        }

        // Try to load from AsyncStorage first
        let state = await loadNavigationState();

        // Fallback to localStorage on web
        if (!state && Platform.OS === 'web') {
          state = webNavigationUtils.getStateFromLocalStorage();
        }

        if (state && isValidNavigationState(state)) {
          console.log('🔄 Restoring navigation state');
          debugNavigationState(state, 'RESTORE');
          isRestoringRef.current = true;
          setInitialNavigationState(state);
          currentStateRef.current = state;
        } else {
          console.log('📱 No valid navigation state to restore');
          if (state) {
            debugNavigationState(state, 'INVALID_STATE');
          }
        }
      } catch (error) {
        console.warn('❌ Failed to restore navigation state:', error);
      } finally {
        setIsNavigationReady(true);
      }
    };

    if (!isNavigationReady) {
      restoreState();
    }
  }, [isNavigationReady]);

  // Setup web-specific navigation handlers
  useEffect(() => {
    if (Platform.OS !== 'web') return;

    const cleanupBrowserNav = webNavigationUtils.setupBrowserNavigation();
    const cleanupPageUnload = webNavigationUtils.setupPageUnloadHandler(getCurrentNavigationState);

    return () => {
      cleanupBrowserNav();
      cleanupPageUnload();
    };
  }, [getCurrentNavigationState]);

  // Handle tab visibility changes to prevent unnecessary resets
  useEffect(() => {
    if (Platform.OS === 'web') {
      const currentState = getCurrentNavigationState();
      if (!isTabActive) {
        // Save current state when tab becomes inactive
        console.log('🔄 Tab became inactive, saving current state...');
        if (currentState) {
          debugNavigationState(currentState, 'TAB_INACTIVE_SAVE');
          saveNavigationState(currentState);
        }
      } else {
        // Tab became active
        console.log('🔄 Tab became active, current state:');
        if (currentState) {
          debugNavigationState(currentState, 'TAB_ACTIVE_CURRENT');
        }
      }
    }
  }, [isTabActive, getCurrentNavigationState]);

  if (isLoading || !isNavigationReady) {
    return null;
  }

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer
        key="main-navigation"
        ref={navigationRef}
        initialState={initialNavigationState}
        onReady={() => {
          console.log('🚀 Navigation container ready');
          if (initialNavigationState) {
            console.log('🔄 Navigation container ready with restored state');
            debugNavigationState(initialNavigationState, 'CONTAINER_READY_WITH_STATE');

            // Force navigation to the correct screen after a short delay
            setTimeout(() => {
              const mainRoute = initialNavigationState.routes?.[initialNavigationState.index];
              if (mainRoute?.name === 'Main' && mainRoute.state) {
                const tabState = mainRoute.state;
                const activeTab = tabState.routes?.[tabState.index];
                if (activeTab && activeTab.name !== 'Home') {
                  console.log(`🔄 Forcing navigation to restored tab: ${activeTab.name}`);
                  navigationRef.navigate(activeTab.name as never);
                }
              }
              // Reset restoration flag after navigation is complete
              setTimeout(() => {
                isRestoringRef.current = false;
                console.log('🔄 Restoration complete, resuming normal state saving');
              }, 200);
            }, 100);
          } else {
            console.log('🔄 Navigation container ready with default state');
          }
          setIsNavigationReady(true);
        }}
        onStateChange={(state) => {
          if (state) {
            debugNavigationState(state, 'STATE_CHANGE');
            currentStateRef.current = state;

            // Don't save state during restoration to prevent overwriting
            if (!isRestoringRef.current) {
              // Save state asynchronously to avoid blocking navigation
              saveNavigationState(state).catch(error => {
                console.warn('Failed to save navigation state:', error);
              });
            } else {
              console.log('🔄 Skipping state save during restoration');
            }
          }
        }}
      >
        <ErrorBoundary>
          <AppNavigator />
        </ErrorBoundary>

        {/* Navigation State Debugger - shows on all screens in development */}
        <NavigationStateDebugger />
      </NavigationContainer>
      <StatusBar style={isDark ? "light" : "dark"} />
    </PaperProvider>
  );
};

const App: React.FC = () => {
  console.log('🎉 Full Kibbutz App Loading...');

  if (typeof document !== 'undefined') {
    document.title = 'Kibbutz App';
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <AuthProvider>
          <ThemeProvider>
            <AppContent />
          </ThemeProvider>
        </AuthProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

export default App;
