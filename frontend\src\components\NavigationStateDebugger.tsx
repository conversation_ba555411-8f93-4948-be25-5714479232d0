import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { useNavigation, useNavigationState } from '@react-navigation/native';
import { loadNavigationState, debugNavigationState } from '../utils/webNavigation';

/**
 * Debug component to help test navigation state persistence
 * Only shows on web platform and in development mode
 */
const NavigationStateDebugger: React.FC = () => {
  const navigation = useNavigation();
  const [savedState, setSavedState] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  // Get current navigation state
  const currentState = useNavigationState(state => state);

  // Only show on web platform and in development
  useEffect(() => {
    const shouldShow = Platform.OS === 'web' && __DEV__;
    setIsVisible(shouldShow);
  }, []);

  // Load saved state for comparison
  useEffect(() => {
    const loadSaved = async () => {
      try {
        const state = await loadNavigationState();
        setSavedState(state);
      } catch (error) {
        console.warn('Failed to load saved state for debug:', error);
      }
    };
    
    if (isVisible) {
      loadSaved();
    }
  }, [isVisible]);

  const handleDebugCurrentState = () => {
    debugNavigationState(currentState, 'DEBUG_CURRENT');
  };

  const handleDebugSavedState = () => {
    debugNavigationState(savedState, 'DEBUG_SAVED');
  };

  const handleNavigateToEvents = () => {
    navigation.navigate('Main', { screen: 'Events' });
  };

  const handleNavigateToJobs = () => {
    navigation.navigate('Main', { screen: 'Jobs' });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Navigation State Debugger</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Route:</Text>
        <Text style={styles.routeText}>
          {currentState?.routes?.[currentState.index]?.name || 'Unknown'}
        </Text>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.button} onPress={handleNavigateToEvents}>
          <Text style={styles.buttonText}>Go to Events</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleNavigateToJobs}>
          <Text style={styles.buttonText}>Go to Jobs</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.debugButton} onPress={handleDebugCurrentState}>
          <Text style={styles.buttonText}>Debug Current</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.debugButton} onPress={handleDebugSavedState}>
          <Text style={styles.buttonText}>Debug Saved</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.instruction}>
        Switch to another browser tab and back to test state persistence
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 8,
    minWidth: 250,
    zIndex: 1000,
  },
  title: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  routeText: {
    color: '#00ff00',
    fontSize: 12,
    fontFamily: 'monospace',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 2,
  },
  debugButton: {
    backgroundColor: '#FF9500',
    padding: 8,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 2,
  },
  buttonText: {
    color: 'white',
    fontSize: 10,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  instruction: {
    color: '#ffff00',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default NavigationStateDebugger;
