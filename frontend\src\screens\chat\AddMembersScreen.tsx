import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import { ChatStackParamList } from '../../navigation/types';
import { AuthService } from '../../services/auth';
import { chatService } from '../../services/chat';
import { useAuth } from '../../contexts/AuthContext';

type AddMembersScreenNavigationProp = StackNavigationProp<ChatStackParamList, 'AddMembers'>;
type AddMembersScreenRouteProp = RouteProp<ChatStackParamList, 'AddMembers'>;

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: string;
}

export default function AddMembersScreen() {
  const navigation = useNavigation<AddMembersScreenNavigationProp>();
  const route = useRoute<AddMembersScreenRouteProp>();
  const { t } = useTranslation();
  const { user } = useAuth();

  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [createdGroupData, setCreatedGroupData] = useState<any>(null);

  const { groupData } = route.params;

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      const fetchedUsers = await AuthService.getAllUsers();
      console.log('👥 Loaded users:', fetchedUsers.length);
      console.log('👤 Current user ID:', user?.id);
      setUsers(fetchedUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      Alert.alert('שגיאה', 'לא ניתן לטעון את רשימת המשתמשים');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(userItem => {
    // Exclude the current user (group creator) from the selection
    if (userItem.id === user?.id) {
      console.log('🚫 Excluding current user from selection:', userItem.first_name, userItem.last_name);
      return false;
    }

    const fullName = `${userItem.first_name} ${userItem.last_name}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  console.log('📋 Filtered users count:', filteredUsers.length, 'out of', users.length, 'total users');

  const toggleMemberSelection = (user: User) => {
    setSelectedMembers(prev => {
      const isSelected = prev.find(member => member.id === user.id);
      if (isSelected) {
        return prev.filter(member => member.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  const removeMember = (userId: string) => {
    setSelectedMembers(prev => prev.filter(member => member.id !== userId));
  };

  const handleCreateGroup = async () => {
    console.log('🎯 handleCreateGroup called');

    if (!user) {
      Alert.alert('שגיאה', 'משתמש לא מחובר');
      return;
    }

    setCreating(true);
    try {
      console.log('🔄 Creating group...');

      // Create the group with selected members
      const memberIds = [user.id, ...selectedMembers.map(member => member.id)];

      const newGroup = {
        name: groupData.name,
        createdAt: new Date(),
        createdBy: user.id,
        members: memberIds,
        iconUrl: null,
        dataAiHint: groupData.description || null,
        lastMessage: null,
        lastMessageTimestamp: null,
      };

      console.log('📤 Sending group creation request:', {
        name: newGroup.name,
        memberCount: memberIds.length,
        type: groupData.type
      });

      console.log('🔍 Full group data being sent:', newGroup);

      // Wait for backend response
      const createdGroup = await chatService.createChatGroup(newGroup);

      console.log('🔍 Full response from backend:', createdGroup);

      console.log('✅ Group created successfully:', {
        id: createdGroup.id,
        name: createdGroup.name,
        memberCount: createdGroup.members?.length || 0
      });

      // Reset creating state and show success
      setCreating(false);
      setCreatedGroupData(createdGroup);
      setShowSuccess(true);

      console.log('🎉 Success state set, should show success modal');
      return; // Exit early to avoid finally block

    } catch (error: any) {
      console.error('❌ Error creating group:', error);

      // Reset creating state and show error
      setCreating(false);

      // Extract error message from different possible error formats
      let extractedErrorMessage = 'אירעה שגיאה לא צפויה ביצירת הקבוצה';

      if (error?.message) {
        extractedErrorMessage = error.message;
      } else if (error?.error) {
        extractedErrorMessage = error.error;
      } else if (error?.response?.data?.error) {
        extractedErrorMessage = error.response.data.error;
      } else if (error?.response?.data?.message) {
        extractedErrorMessage = error.response.data.message;
      } else if (typeof error === 'string') {
        extractedErrorMessage = error;
      }

      setErrorMessage(extractedErrorMessage);
      setShowError(true);

      console.log('❌ Error state set, should show error modal');
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName[0]}${lastName[0]}`.toUpperCase();
  };

  const getAvatarColor = (userId: string) => {
    const colors = ['#e74c3c', '#3498db', '#2ecc71', '#9b59b6', '#f39c12', '#e67e22', '#27ae60', '#8e44ad'];
    const index = userId.length % colors.length;
    return colors[index];
  };

  const renderSelectedChip = ({ item }: { item: User }) => (
    <View style={styles.selectedChip}>
      <View style={[styles.chipAvatar, { backgroundColor: getAvatarColor(item.id) }]}>
        <Text style={styles.chipAvatarText}>{getInitials(item.first_name, item.last_name)}</Text>
      </View>
      <Text style={styles.chipName}>{item.first_name} {item.last_name[0]}.</Text>
      <TouchableOpacity onPress={() => removeMember(item.id)} style={styles.chipRemove}>
        <Text style={styles.chipRemoveText}>×</Text>
      </TouchableOpacity>
    </View>
  );

  const renderUserItem = ({ item }: { item: User }) => {
    const isSelected = selectedMembers.find(member => member.id === item.id);
    
    return (
      <TouchableOpacity
        style={styles.contactItem}
        onPress={() => toggleMemberSelection(item)}
      >
        <View style={[styles.contactAvatar, { backgroundColor: getAvatarColor(item.id) }]}>
          <Text style={styles.contactAvatarText}>{getInitials(item.first_name, item.last_name)}</Text>
        </View>
        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{item.first_name} {item.last_name}</Text>
          <Text style={styles.contactStatus}>{item.role}</Text>
        </View>
        <View style={[styles.contactCheckbox, isSelected && styles.contactCheckboxSelected]}>
          {isSelected && <View style={styles.checkboxInner} />}
        </View>
      </TouchableOpacity>
    );
  };

  const createButton = (
    <TouchableOpacity
      style={[styles.createButton, (selectedMembers.length === 0 || creating) && styles.createButtonDisabled]}
      onPress={handleCreateGroup}
      disabled={selectedMembers.length === 0 || creating}
    >
      <View style={styles.createButtonContent}>
        {creating && (
          <ActivityIndicator
            size="small"
            color="white"
            style={styles.createButtonSpinner}
          />
        )}
        <Text style={styles.createButtonText}>
          {creating ? 'יוצר קבוצה...' : 'צור'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Custom Header with Step Info */}
      <View style={styles.customHeader}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>הוסף חברים</Text>
          <Text style={styles.headerSubtitle}>
            {selectedMembers.length + 1} חברים (כולל אתה)
          </Text>
        </View>
        {createButton}
      </View>

      {/* Search Container */}
      <View style={styles.searchContainer}>
        <TextInput
          style={[styles.searchInput, { textAlign: getTextAlign() }]}
          placeholder="חפש אנשי קשר..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#95a5a6"
        />
        <Text style={styles.searchIcon}>🔍</Text>
      </View>

      {/* Selected Members */}
      {selectedMembers.length > 0 && (
        <View style={styles.selectedMembers}>
          <Text style={styles.selectedTitle}>חברים נבחרים</Text>
          <FlatList
            data={selectedMembers}
            renderItem={renderSelectedChip}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.selectedChips}
          />
        </View>
      )}

      {/* Contacts List */}
      <View style={styles.contactsList}>
        <View style={styles.contactsHeader}>
          <Text style={styles.contactsTitle}>אנשי קשר</Text>
          <Text style={styles.creatorNote}>💡 אתה נכלל אוטומטית כמנהל הקבוצה</Text>
        </View>
        <FlatList
          data={filteredUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* Loading Overlay */}
      {creating && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.loadingText}>יוצר קבוצה...</Text>
            <Text style={styles.loadingSubtext}>אנא המתן</Text>
          </View>
        </View>
      )}

      {/* Success Modal */}
      {showSuccess && (
        <View style={styles.successOverlay}>
          <View style={styles.successContainer}>
            <Text style={styles.successIcon}>🎉</Text>
            <Text style={styles.successTitle}>הצלחה!</Text>
            <Text style={styles.successMessage}>
              הקבוצה "{groupData.name}" נוצרה בהצלחה!
            </Text>
            <Text style={styles.successDetails}>
              חברים: {selectedMembers.length + 1} (כולל אתה){'\n'}
              סוג: {groupData.type === 'private' ? 'פרטית' : 'ציבורית'}
            </Text>
            <TouchableOpacity
              style={styles.successButton}
              onPress={() => {
                console.log('🏠 User clicked OK, navigating to ChatList');
                setShowSuccess(false);
                navigation.navigate('ChatList');
              }}
            >
              <Text style={styles.successButtonText}>אישור</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Error Modal */}
      {showError && (
        <View style={styles.errorOverlay}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorIcon}>❌</Text>
            <Text style={styles.errorTitle}>שגיאה ביצירת הקבוצה</Text>
            <Text style={styles.errorMessage}>
              לא ניתן ליצור את הקבוצה "{groupData.name}".
            </Text>
            <Text style={styles.errorDetails}>
              סיבה: {errorMessage}{'\n\n'}
              אנא בדוק את החיבור לאינטרנט ונסה שוב.
            </Text>
            <View style={styles.errorButtons}>
              <TouchableOpacity
                style={[styles.errorButton, styles.retryButton]}
                onPress={() => {
                  console.log('🔄 User chose to retry');
                  setShowError(false);
                  setErrorMessage('');
                }}
              >
                <Text style={styles.retryButtonText}>נסה שוב</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.errorButton, styles.cancelButton]}
                onPress={() => {
                  console.log('🚫 User cancelled after error');
                  setShowError(false);
                  setErrorMessage('');
                  navigation.goBack();
                }}
              >
                <Text style={styles.cancelButtonText}>ביטול</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  // Custom Header
  customHeader: {
    backgroundColor: '#667eea',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: Platform.OS === 'android' ? 35 : 15,
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  headerSubtitle: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
  },
  searchContainer: {
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
    position: 'relative',
  },
  searchInput: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    paddingRight: 45,
    borderWidth: 2,
    borderColor: '#e1e8ed',
    borderRadius: 25,
    fontSize: 14,
    backgroundColor: '#f8f9fa',
  },
  searchIcon: {
    position: 'absolute',
    right: 30,
    top: '50%',
    transform: [{ translateY: -10 }],
    color: '#95a5a6',
  },
  selectedMembers: {
    padding: 15,
    backgroundColor: '#f8f9ff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
  },
  selectedTitle: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '600',
    marginBottom: 10,
  },
  selectedChips: {
    flexGrow: 0,
  },
  selectedChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chipAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chipAvatarText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  chipName: {
    fontSize: 12,
    color: '#2c3e50',
  },
  chipRemove: {
    marginLeft: 8,
    padding: 2,
  },
  chipRemoveText: {
    color: '#95a5a6',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contactsList: {
    flex: 1,
    padding: 15,
  },
  contactsHeader: {
    marginBottom: 15,
  },
  contactsTitle: {
    fontSize: 12,
    color: '#95a5a6',
    fontWeight: '600',
    marginBottom: 5,
  },
  creatorNote: {
    fontSize: 11,
    color: '#667eea',
    fontStyle: 'italic',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  contactAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactAvatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontWeight: '600',
    color: '#2c3e50',
    fontSize: 14,
  },
  contactStatus: {
    fontSize: 12,
    color: '#95a5a6',
  },
  contactCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#e1e8ed',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactCheckboxSelected: {
    borderColor: '#667eea',
  },
  checkboxInner: {
    width: 10,
    height: 10,
    backgroundColor: '#667eea',
    borderRadius: 2,
  },
  createButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 15,
  },
  createButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  createButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonSpinner: {
    marginRight: 8,
  },
  createButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  // Loading Overlay
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
  },
  loadingSubtext: {
    marginTop: 5,
    fontSize: 14,
    color: '#95a5a6',
    textAlign: 'center',
  },
  // Success Modal
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1001,
  },
  successContainer: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxWidth: '80%',
  },
  successIcon: {
    fontSize: 48,
    marginBottom: 15,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: '#2c3e50',
    marginBottom: 15,
    textAlign: 'center',
  },
  successDetails: {
    fontSize: 14,
    color: '#95a5a6',
    marginBottom: 25,
    textAlign: 'center',
    lineHeight: 20,
  },
  successButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  successButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  // Error Modal
  errorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1002,
  },
  errorContainer: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxWidth: '80%',
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 15,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#2c3e50',
    marginBottom: 15,
    textAlign: 'center',
  },
  errorDetails: {
    fontSize: 14,
    color: '#95a5a6',
    marginBottom: 25,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorButtons: {
    flexDirection: 'row',
    gap: 15,
  },
  errorButton: {
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 25,
    minWidth: 100,
    alignItems: 'center',
  },
  retryButton: {
    backgroundColor: '#667eea',
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  cancelButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
});
