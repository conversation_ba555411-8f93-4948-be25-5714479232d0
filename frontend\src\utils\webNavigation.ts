import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Web-specific navigation utilities to handle browser tab switching
 * and navigation state persistence
 */

export const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';
export const NAVIGATION_READY_KEY = 'NAVIGATION_READY_V1';

/**
 * Safely save navigation state to storage
 */
export const saveNavigationState = async (state: any): Promise<void> => {
  try {
    if (!state) {
      console.warn('⚠️ Attempted to save null/undefined navigation state');
      return;
    }

    const stateString = JSON.stringify(state);
    await AsyncStorage.setItem(NAVIGATION_PERSISTENCE_KEY, stateString);
    
    // Also save timestamp to track when state was last saved
    await AsyncStorage.setItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`, Date.now().toString());
    
    console.log('💾 Navigation state saved successfully');
  } catch (error) {
    console.error('❌ Failed to save navigation state:', error);
  }
};

/**
 * Safely load navigation state from storage
 */
export const loadNavigationState = async (): Promise<any | null> => {
  try {
    const stateString = await AsyncStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
    
    if (!stateString) {
      console.log('📱 No saved navigation state found');
      return null;
    }

    const state = JSON.parse(stateString);
    
    // Check if state is valid and not too old (optional: expire after 24 hours)
    const timestampString = await AsyncStorage.getItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
    if (timestampString) {
      const timestamp = parseInt(timestampString, 10);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (now - timestamp > maxAge) {
        console.log('⏰ Saved navigation state is too old, ignoring');
        await clearNavigationState();
        return null;
      }
    }

    console.log('📱 Navigation state loaded successfully');
    return state;
  } catch (error) {
    console.error('❌ Failed to load navigation state:', error);
    return null;
  }
};

/**
 * Clear saved navigation state
 */
export const clearNavigationState = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(NAVIGATION_PERSISTENCE_KEY);
    await AsyncStorage.removeItem(`${NAVIGATION_PERSISTENCE_KEY}_timestamp`);
    await AsyncStorage.removeItem(NAVIGATION_READY_KEY);
    console.log('🧹 Navigation state cleared');
  } catch (error) {
    console.error('❌ Failed to clear navigation state:', error);
  }
};

/**
 * Check if navigation state should be restored based on platform and conditions
 */
export const shouldRestoreNavigationState = (): boolean => {
  // Always restore on web to prevent tab switching issues
  if (Platform.OS === 'web') {
    return true;
  }

  // On mobile, restore only if app was backgrounded recently
  return true;
};

/**
 * Debug function to log navigation state details
 */
export const debugNavigationState = (state: any, context: string): void => {
  if (!state) {
    console.log(`🔍 [${context}] Navigation state is null/undefined`);
    return;
  }

  console.log(`🔍 [${context}] Navigation state:`, {
    routesCount: state.routes?.length || 0,
    currentIndex: state.index,
    currentRoute: state.routes?.[state.index]?.name,
    stateKeys: Object.keys(state),
  });
};

/**
 * Validate navigation state structure
 */
export const isValidNavigationState = (state: any): boolean => {
  if (!state || typeof state !== 'object') {
    return false;
  }

  // Basic validation - check for required navigation state properties
  if (!state.routes || !Array.isArray(state.routes) || state.routes.length === 0) {
    return false;
  }

  // Check if state has valid structure
  if (typeof state.index !== 'number' || state.index < 0 || state.index >= state.routes.length) {
    return false;
  }

  return true;
};

/**
 * Web-specific navigation state handling
 */
export const webNavigationUtils = {
  /**
   * Handle browser back/forward navigation
   */
  setupBrowserNavigation: () => {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {}; // No-op cleanup function
    }

    const handlePopState = (event: PopStateEvent) => {
      console.log('🔄 Browser navigation detected:', event.state);
      // Handle browser back/forward if needed
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  },

  /**
   * Handle page unload to save state
   */
  setupPageUnloadHandler: (getCurrentState: () => any) => {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {}; // No-op cleanup function
    }

    const handleBeforeUnload = () => {
      const currentState = getCurrentState();
      if (currentState) {
        // Use synchronous storage for page unload
        try {
          localStorage.setItem(NAVIGATION_PERSISTENCE_KEY, JSON.stringify(currentState));
        } catch (error) {
          console.warn('Failed to save state on page unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  },

  /**
   * Get state from localStorage as fallback
   */
  getStateFromLocalStorage: (): any | null => {
    if (Platform.OS !== 'web' || typeof localStorage === 'undefined') {
      return null;
    }

    try {
      const stateString = localStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
      return stateString ? JSON.parse(stateString) : null;
    } catch (error) {
      console.warn('Failed to get state from localStorage:', error);
      return null;
    }
  },
};
