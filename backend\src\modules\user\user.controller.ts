import { Request, Response } from 'express';
import { UserService } from './user.service';
import { CreateUserDTO } from './user.types';
import { getModuleLogger } from '../../utils/logger';
import { getSupabaseClient } from '../../config/supabase';
import { Prisma } from '../../generated/prisma';

const logger = getModuleLogger('User');

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  private validateCreateUserData(data: any): { isValid: boolean; error?: string } {
    // Check required fields
    const requiredFields = ['email', 'password', 'role', 'userType'];
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        error: 'Invalid email format'
      };
    }

    // Validate role
    if (!['ADMIN', 'MODERATOR', 'USER'].includes(data.role)) {
      return {
        isValid: false,
        error: 'Invalid role'
      };
    }

    // Validate userType
    if (!['ADULT', 'YOUTH', 'CHILD', 'EXTERNAL'].includes(data.userType)) {
      return {
        isValid: false,
        error: 'Invalid user type'
      };
    }

    return { isValid: true };
  }

  private validateCreateUserProfileData(data: any): { isValid: boolean; error?: string } {
    // Check required fields
    const requiredFields = ['id', 'email', 'role', 'userType'];
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        error: 'Invalid email format'
      };
    }

    // Validate role if provided
    if (data.role && !['ADMIN', 'MODERATOR', 'USER'].includes(data.role)) {
      return {
        isValid: false,
        error: 'Invalid role'
      };
    }

    // Validate userType if provided
    if (data.userType && !['ADULT', 'YOUTH', 'CHILD', 'EXTERNAL'].includes(data.userType)) {
      return {
        isValid: false,
        error: 'Invalid user type'
      };
    }

    return { isValid: true };
  }

  async createUser(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Creating new user');
      const user = await this.userService.createUser(req.body as CreateUserDTO);
      logger.info('Successfully created new user');
      res.status(201).json({ success: true, data: user });
    } catch (error) {
      logger.error(`Error in createUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to create user' });
    }
  }

  async createUserProfile(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Creating user profile');
      const userProfile = await this.userService.createUserProfile(req.body);
      logger.info('Successfully created user profile');
      res.status(201).json({
        success: true,
        data: userProfile
      });
    } catch (error) {
      logger.error(`Error in createUserProfile: ${error}`);
      res.status(500).json({ error: 'Failed to create user profile' });
    }
  }

  async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      logger.info(`Updating user with ID: ${id}`);
      const user = await this.userService.updateUser(id, req.body);
      logger.info(`Successfully updated user with ID: ${id}`);
      res.json({ success: true, data: user });
    } catch (error) {
      logger.error(`Error in updateUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to update user' });
    }
  }

  async getUserById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      // Check if user is self or admin
      const tokenUser = req.user as { id: string; role: string };
      if (tokenUser.id !== id && tokenUser.role !== 'ADMIN') {
        res.status(403).json({ success: false, error: 'Forbidden' });
        return;
      }
      logger.info(`Fetching user with ID: ${id}`);
      const user = await this.userService.getUserById(id);
      if (!user) {
        logger.warn(`User not found for ID: ${id}`);
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }
      logger.info(`Successfully fetched user with ID: ${id}`);
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error(`Error in getUserById: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch user'
      });
    }
  }

  async getAllUsers(_req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching all users');
      const users = await this.userService.getAllUsers();
      logger.info('Successfully fetched all users');
      res.json({ success: true, data: users });
    } catch (error) {
      logger.error(`Error in getAllUsers: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to get users' });
    }
  }

  async getUserCount(_req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching user count');
      const count = await this.userService.getUserCount();
      logger.info('Successfully fetched user count');
      res.json({
        success: true,
        data: count
      });
    } catch (error) {
      logger.error(`Error in getUserCount: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to get user count'
      });
    }
  }

  async getUsersByTypeOrRole(_req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching users by type or role');
      const users = await this.userService.getUsersByTypeOrRole();
      logger.info('Successfully fetched users by type or role');
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      logger.error(`Error in getUsersByTypeOrRole: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch users by type or role'
      });
    }
  }

  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      logger.info(`Deleting user with ID: ${id}`);
      await this.userService.deleteUser(id);
      logger.info(`Successfully deleted user with ID: ${id}`);
      res.json({ success: true, message: 'User deleted successfully' });
    } catch (error) {
      logger.error(`Error in deleteUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to delete user' });
    }
  }

  async updateUIPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      logger.info(`Updating UI preferences for user with ID: ${id}`);
      const user = await this.userService.updateUIPreferences(id, req.body.uiPreferences);
      logger.info(`Successfully updated UI preferences for user with ID: ${id}`);
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error(`Error in updateUIPreferences: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to update UI preferences'
      });
    }
  }

  async getUserUIPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      logger.info(`Fetching UI preferences for user with ID: ${id}`);
      const preferences = await this.userService.getUserUIPreferences(id);
      if (!preferences) {
        logger.warn(`UI preferences not found for user ID: ${id}`);
        res.status(404).json({
          success: false,
          error: 'UI preferences not found'
        });
        return;
      }
      logger.info(`Successfully fetched UI preferences for user with ID: ${id}`);
      res.json({
        success: true,
        data: preferences
      });
    } catch (error) {
      logger.error(`Error in getUserUIPreferences: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch UI preferences'
      });
    }
  }

  async getPeople(_req: Request, res: Response) {
    try {
      logger.info('Fetching people directory');
      const people = await this.userService.getPeople();
      logger.info('Successfully fetched people directory');
      res.json(people);
    } catch (error) {
      logger.error(`Error in getPeople controller: ${error}`);
      res.status(500).json({ error: 'Failed to fetch people' });
    }
  }
}